/// 登录类型枚举
enum LoginType {
  /// 手机号登录
  phone,
  /// 微信登录
  wechat,
  /// 账号登录
  account,
}

/// 用户详细信息（除登录态外的用户信息）
class UserDetail {
  /// 用户名
  final String? uname;
  
  /// 别名
  final String? alias;
  
  /// 手机号
  final String? mobile;
  
  /// 头像URL
  final String? avatar;
  
  /// 创建时间
  final DateTime? createdAt;

  const UserDetail({
    this.uname,
    this.alias,
    this.mobile,
    this.avatar,
    this.createdAt,
  });

  /// 获取显示名称
  String get displayName {
    // 按优先级显示：别名 > 用户名 > 手机号
    if (alias != null && alias!.isNotEmpty) {
      return alias!;
    }
    if (uname != null && uname!.isNotEmpty) {
      return uname!;
    }
    if (mobile != null && mobile!.isNotEmpty) {
      return mobile!;
    }
    return '未知用户';
  }

  factory UserDetail.fromJson(Map<String, dynamic> json) {
    return UserDetail(
      uname: json['uname'] as String?,
      alias: json['alias'] as String?,
      mobile: json['mobile'] as String?,
      avatar: json['avatar'] as String?,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uname': uname,
      'alias': alias,
      'mobile': mobile,
      'avatar': avatar,
      'createdAt': createdAt?.toIso8601String(),
    };
  }

  UserDetail copyWith({
    String? uname,
    String? alias,
    String? mobile,
    String? avatar,
    DateTime? createdAt,
  }) {
    return UserDetail(
      uname: uname ?? this.uname,
      alias: alias ?? this.alias,
      mobile: mobile ?? this.mobile,
      avatar: avatar ?? this.avatar,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'UserDetail(displayName: $displayName, mobile: $mobile)';
  }
}