import 'login_info.dart';
import 'user_detail.dart';

/// 完整的用户对象，包含登录信息和用户详细信息
class User {
  /// 登录相关信息（登录态、票据等数据）
  final LoginInfo loginInfo;
  
  /// 用户详细信息（展示相关，主要用于展示）
  final UserDetail userDetail;

  const User({
    required this.loginInfo,
    required this.userDetail,
  });

  /// 获取用户ID（muid）
  String? get muid => loginInfo.muid;
  
  
  /// 获取用户名
  String? get uname => userDetail.uname;
  
  /// 获取显示名称
  String get displayName {
    // 如果是手机号登录，优先显示手机号
    if (loginInfo.loginType == 'phone' && userDetail.mobile != null && userDetail.mobile!.isNotEmpty) {
      return userDetail.mobile!;
    }
    // 其他情况使用 UserDetail 的显示逻辑
    return userDetail.displayName;
  }
  
  /// 获取登录类型
  String get loginType => loginInfo.loginType;
  
  /// 获取登录类型枚举
  LoginType get loginTypeEnum {
    switch (loginInfo.loginType) {
      case 'phone':
        return LoginType.phone;
      case 'wechat':
        return LoginType.wechat;
      case 'account':
        return LoginType.account;
      default:
        return LoginType.account;
    }
  }
  
  /// 获取票据
  String get ticket => loginInfo.ticket;
  
  /// 获取刷新票据
  String get refreshTicket => loginInfo.refreshTicket;

  /// 从登录结果创建用户对象
  factory User.fromLoginInfo(
    LoginInfo loginInfo, {
    String? mobile,
    DateTime? createdAt,
  }) {
    final userDetail = UserDetail(
      uname: loginInfo.muname,
      alias: loginInfo.muname,
      mobile: mobile,
      avatar: null,
      createdAt: createdAt ?? DateTime.now(),
    );
    
    return User(
      loginInfo: loginInfo,
      userDetail: userDetail,
    );
  }

  /// 从JSON创建（向后兼容旧的UserInfo格式）
  factory User.fromJson(Map<String, dynamic> json) {
    // 如果是新格式（包含loginInfo和userDetail）
    if (json.containsKey('loginInfo') && json.containsKey('userDetail')) {
      return User(
        loginInfo: LoginInfo.fromJson(json['loginInfo']),
        userDetail: UserDetail.fromJson(json['userDetail']),
      );
    }
    
    // 如果是旧格式（UserInfo），需要适配
    final loginInfo = LoginInfo(
      muname: json['uname'] as String?,
      muid: json['userId']?.toString(),
      loginType: json['loginType'] as String? ?? '',
      ticket: json['ticket'] as String? ?? '',
      refreshTicket: json['refreshTicket'] as String? ?? '',
      lastLoginAt: json['lastLoginAt'] != null ? DateTime.parse(json['lastLoginAt']) : null,
    );
    
    final userDetail = UserDetail(
      uname: json['uname'] as String?,
      alias: json['alias'] as String?,
      mobile: json['mobile'] as String?,
      avatar: json['avatar'] as String?,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
    );
    
    return User(
      loginInfo: loginInfo,
      userDetail: userDetail,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'loginInfo': loginInfo.toJson(),
      'userDetail': userDetail.toJson(),
    };
  }

  /// 向后兼容的JSON格式（UserInfo格式）
  Map<String, dynamic> toUserInfoJson() {
    return {
      'userId': muid,  // 向后兼容，输出为userId但值来自muid
      'uname': userDetail.uname,
      'alias': userDetail.alias,
      'mobile': userDetail.mobile,
      'loginType': loginInfo.loginType,
      'avatar': userDetail.avatar,
      'createdAt': userDetail.createdAt?.toIso8601String(),
      'lastLoginAt': loginInfo.lastLoginAt?.toIso8601String(),
    };
  }

  User copyWith({
    LoginInfo? loginInfo,
    UserDetail? userDetail,
  }) {
    return User(
      loginInfo: loginInfo ?? this.loginInfo,
      userDetail: userDetail ?? this.userDetail,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.muid == muid;
  }

  @override
  int get hashCode => muid.hashCode;

  @override
  String toString() {
    return 'User(muid: $muid, displayName: $displayName, loginType: $loginType)';
  }
}