import 'package:flutter/foundation.dart';
import '../push/sq_push_manager.dart';
import '../services/user_storage_service.dart';
import '../model/user.dart';
import '../services/login_api_service.dart';

/// 用户数据存储和更新通知
/// 负责用户会话管理、数据持久化、状态更新
class UserProvider extends ChangeNotifier {
  final UserStorageService _storageService = UserStorageService();
  
  User? _currentUser;
  List<User> _userList = [];
  bool _isLoggedIn = false;
  bool _isInitialized = false;

  // Getters
  User? get currentUser => _currentUser;
  List<User> get userList => _userList;
  bool get isLoggedIn => _isLoggedIn;
  bool get isInitialized => _isInitialized;

  /// 获取当前用户ID
  String? get currentUserId => _currentUser?.muid;

  /// 获取当前用户显示名称
  String get currentUserDisplayName => _currentUser?.displayName ?? '';

  /// 初始化用户数据
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // 从本地存储加载用户数据
      _currentUser = await _storageService.getCurrentUser();
      _userList = await _storageService.getUserList();
      _isLoggedIn = _currentUser != null && _isValidSession(_currentUser!);
      if (_isLoggedIn) {
        SqPushManager().tryPushToken(_currentUser?.muid ?? "");
      }
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('用户数据初始化失败: $e');
      _isInitialized = true;
      notifyListeners();
    }
  }

  /// 登录成功处理
  Future<void> loginSuccess({User? user}) async {
    if (user != null) {
      _currentUser = user;
      await _storageService.saveCurrentUser(user);
      await _updateUserList(user);
      SqPushManager().tryPushToken(_currentUser?.muid ?? "");
    }
    
    _isLoggedIn = true;
    notifyListeners();
  }

  /// 退出登录
  Future<void> logout() async {
    try {
      // 保存当前用户到历史记录（如果有的话）
      if (_currentUser != null) {
        await _updateUserList(_currentUser!);
      }
      
      // 清除当前用户状态
      _currentUser = null;
      _isLoggedIn = false;
      
      // 清除本地存储的当前用户数据
      await _storageService.saveCurrentUser(null);
      
      // 这里可以添加其他清理逻辑，比如：
      // - 清除缓存数据
      // - 取消网络请求
      // - 清除敏感信息
      
      notifyListeners();
    } catch (e) {
      debugPrint('退出登录失败: $e');
      // 即使出错也要清除状态，确保用户能够退出
      _currentUser = null;
      _isLoggedIn = false;
      notifyListeners();
    }
  }

  /// 更新用户信息
  Future<void> updateUserInfo(User user) async {
    _currentUser = user;
    await _storageService.saveCurrentUser(user);
    await _updateUserList(user);
    notifyListeners();
  }

  /// 切换用户账号
  Future<bool> switchUser(User user) async {
    try {
      // 先调用刷新票据接口验证账号有效性
      final api = LoginApiService();
      final resp = await api.refreshTicket(
        appTicket: user.ticket,
        appRefreshTicket: user.refreshTicket,
      );

      if (resp.success && resp.data != null) {
        // 刷新成功，更新用户信息
        final newInfo = resp.data!;
        final updatedUser = user.copyWith(
          loginInfo: user.loginInfo.copyWith(
            ticket: newInfo.ticket,
            refreshTicket: newInfo.refreshTicket,
            lastLoginAt: DateTime.now(), // 切换用户刷新票据时更新登录时间
          ),
        );
        
        _currentUser = updatedUser;
        _isLoggedIn = _isValidSession(updatedUser);
        
        await _storageService.saveCurrentUser(updatedUser);
        await _updateUserList(updatedUser);
        SqPushManager().tryPushToken(_currentUser?.muid ?? "");
        notifyListeners();
        return true;
      } else {
        // 刷新失败，账号可能已失效
        debugPrint('切换账号失败: 票据刷新失败 - ${resp.message}');
        return false;
      }
    } catch (e) {
      debugPrint('切换账号失败: $e');
      return false;
    }
  }

  /// 删除历史账号
  Future<void> removeUser(String userId) async {
    _userList.removeWhere((user) => user.muid == userId);
    // 保存更新后的列表（会自动排序）
    await _storageService.saveUserList(_userList);
    
    // 如果删除的是当前用户，需要清除当前用户
    if (_currentUser?.muid == userId) {
      await logout();
    }
    
    notifyListeners();
  }

  /// 清除所有用户数据
  Future<void> clearAllUsers() async {
    await _storageService.clearAllUsers();
    _userList.clear();
    _currentUser = null;
    _isLoggedIn = false;
    notifyListeners();
  }

  /// 获取用户历史登录记录（按最后登录时间排序，最新的在前）
  List<User> getRecentUsers({int limit = 5}) {
    return _userList.take(limit).toList();
  }

  /// 检查用户是否在历史记录中
  bool hasUserInHistory(String userId) {
    return _userList.any((user) => user.muid == userId);
  }

  /// 根据用户ID获取用户信息
  User? getUserById(String userId) {
    try {
      return _userList.firstWhere((user) => user.muid == userId);
    } catch (e) {
      return null;
    }
  }

  /// 获取当前用户的登录票据
  String? get currentTicket => _currentUser?.ticket;

  /// 获取当前用户的刷新票据
  String? get currentRefreshTicket => _currentUser?.refreshTicket;

  /// 检查是否需要刷新票据
  bool get needsTokenRefresh {
    if (_currentUser == null) return false;
    // 这里可以添加票据过期时间检查逻辑
    // 目前简单返回false，实际应用中需要根据票据时间戳判断
    return false;
  }
  
  /// 更新用户列表
  Future<void> _updateUserList(User user) async {
    // 移除已存在的相同用户
    _userList.removeWhere((u) => u.muid == user.muid);
    // 添加用户（存储服务会自动按 lastLoginAt 排序）
    _userList.add(user);
    // 限制历史账号数量
    if (_userList.length > 5) {
      _userList = _userList.take(5).toList();
    }
    
    // 保存到存储服务（会自动排序）
    await _storageService.saveUserList(_userList);
    // 重新加载以确保本地列表也是排序后的
    _userList = await _storageService.getUserList();
  }

  /// 验证用户会话是否有效
  bool _isValidSession(User user) {
    // 检查用户票据是否存在
    if (user.ticket.isEmpty) {
      return false;
    }
    
    // 这里可以添加更多的会话验证逻辑
    // 比如检查票据是否过期、是否需要刷新等
    
    return true;
  }

  /// 刷新用户票据
  Future<bool> refreshUserToken() async {
    if (_currentUser == null) return false;
    
    try {
      final api = LoginApiService();
      final resp = await api.refreshTicket(
        appTicket: _currentUser!.ticket,
        appRefreshTicket: _currentUser!.refreshTicket,
      );

      if (resp.success && resp.data != null) {
        final newInfo = resp.data!;
        final updatedUser = _currentUser!.copyWith(
          loginInfo: _currentUser!.loginInfo.copyWith(
            ticket: newInfo.ticket,
            refreshTicket: newInfo.refreshTicket,
            lastLoginAt: DateTime.now(), // 刷新票据时更新登录时间
          ),
        );
        _currentUser = updatedUser;
        await _storageService.saveCurrentUser(updatedUser);
        await _updateUserList(updatedUser);
        notifyListeners();
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('刷新用户票据失败: $e');
      return false;
    }
  }

  /// 获取用户统计信息
  Map<String, int> getUserStats() {
    final stats = <String, int>{};
    
    for (final user in _userList) {
      final loginType = user.loginType;
      stats[loginType] = (stats[loginType] ?? 0) + 1;
    }
    
    return stats;
  }

  /// 按登录类型获取用户列表
  List<User> getUsersByLoginType(String loginType) {
    return _userList.where((user) => user.loginType == loginType).toList();
  }

  @override
  void dispose() {
    super.dispose();
  }
}