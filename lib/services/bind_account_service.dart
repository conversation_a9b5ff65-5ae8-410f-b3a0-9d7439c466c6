import 'package:dlyz_flutter/net/sign_interceptor.dart';

import '../net/http_service.dart';
import '../net/http_base_response.dart';
import '../model/game_role.dart';

/// 绑定账号服务类
class BindAccountService {
  static final BindAccountService _instance = BindAccountService._internal();
  factory BindAccountService() => _instance;
  BindAccountService._internal();

  HttpService get _httpService => HttpService.getInstance();
  
  // API接口配置
  static const String _baseUrl = 'http://gamehub-api.37.com.cn';
  static const String _roleListPath = '/api/role-kit/v1/gamehub_role_lister/by_tgame';
  static const String _roleListV2Path = '/api/gamehub-api/v1/role/get_list';
  static const String _rolePickPath = '/api/gamehub-api/v1/role/pick';
  
  /// 获取角色列表
  /// 
  /// [tgid] 游戏ID
  /// 返回 [Future<BaseResponse<GameRoleListData>>] 角色列表结果
  /// 
  /// 注意：其他公共参数（pid、gid、appid、sversion、gwversion、os、dev、time、app_ticket、oaid/caid、sign）
  /// 由CommonParamsInterceptor和SignInterceptor自动添加
  Future<BaseResponse<GameRoleListData>> getRoleList({
    required String tgid,
  }) async {
    try {
      // 构建请求参数 - 只传递API特有参数，公共参数由拦截器自动添加
      final Map<String, dynamic> params = {
        'tgid': tgid,
      };

      // 发起请求
      return await _httpService.post<GameRoleListData>(
        _roleListPath,
        baseUrl: _baseUrl,
        data: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) => GameRoleListData.fromJson(data),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 获取角色列表V2 (接口二)
  /// 
  /// [tgid] 游戏ID
  /// 返回 [Future<BaseResponse<GameRoleListV2Data>>] 角色列表结果
  /// 
  /// 注意：其他公共参数（pid、gid、appid、sversion、gwversion、os、dev、time、app_ticket、oaid/caid、sign）
  /// 由CommonParamsInterceptor和SignInterceptor自动添加
  Future<BaseResponse<GameRoleListV2Data>> getRoleListV2({
    required String tgid,
  }) async {
    try {
      // 构建请求参数 - 只传递API特有参数，公共参数由拦截器自动添加
      final Map<String, dynamic> params = {
        'tgid': tgid,
      };

      // 发起请求
      return await _httpService.post<GameRoleListV2Data>(
        _roleListV2Path,
        baseUrl: _baseUrl,
        data: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) => GameRoleListV2Data.fromJson(data),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 选择角色 (接口三)
  /// 
  /// [roleFavoriteId] 角色收藏ID
  /// [uid] 用户ID
  /// [rolePid] 角色项目ID
  /// [roleGid] 角色游戏ID
  /// [drid] 角色ID
  /// [dsid] 服务器ID
  /// [drname] 角色名称
  /// [dsname] 服务器名称
  /// 返回 [Future<BaseResponse<RolePickResponseData>>] 选择角色结果
  /// 
  /// 注意：其他公共参数（pid、gid、appid、sversion、gwversion、os、dev、time、app_ticket、oaid/caid、sign）
  /// 由CommonParamsInterceptor和SignInterceptor自动添加
  Future<BaseResponse<RolePickResponseData>> pickRole({
    required String roleFavoriteId,
    required String uid,
    required String rolePid,
    required String roleGid,
    required String drid,
    required String dsid,
    required String drname,
    required String dsname,
  }) async {
    try {
      // 构建请求参数 - 只传递API特有参数，公共参数由拦截器自动添加
      final Map<String, dynamic> params = {
        'role_favorite_id': roleFavoriteId,
        'uid': uid,
        'role_pid': rolePid,
        'role_gid': roleGid,
        'drid': drid,
        'dsid': dsid,
        'drname': drname,
        'dsname': dsname,
      };

      // 发起请求
      return await _httpService.post<RolePickResponseData>(
        _rolePickPath,
        baseUrl: _baseUrl,
        data: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) => RolePickResponseData.fromJson(data),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }
}