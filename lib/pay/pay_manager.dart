import 'package:dlyz_flutter/pay/pay_type.dart';

import '../manager/channel_manager.dart';

class PayManager {
  static final PayManager _instance = PayManager._internal();

  factory PayManager() => _instance;

  PayManager._internal();

  static getInstance() {
    return _instance;
  }

  Future<Map<String, String>> pay(Map<String, dynamic> params) async {
    String payType = params['payType'] ?? '';
    PayType? payTypeEnum;
    try {
      payTypeEnum = PayType.values.firstWhere((e) => e.name == payType);
    } catch (e) {
      payTypeEnum = null;
    }
    if (payTypeEnum == null) {
      return {'code': PayResult.PAY_FAILED, 'message': '不支持的支付类型'};
    }

  }

  Future<Map<String, String>> alipayAndroid(Map<String, dynamic> params) async {
    String orderId = params['uuid'] ?? '';
    String tradeInfo = params['trade'] ?? '';
    if (tradeInfo.isEmpty) {
      return {'code': PayResult.PAY_FAILED, 'message': '支付信息不能为空'};
    }
    PayResult payResult = await ChannelManager().alipay(
      payType: PayType.alipay,
      orderId: orderId,
      tradeInfo: tradeInfo,
    );
    return {'code': payResult.code, 'message': payResult.message};
  }
}
